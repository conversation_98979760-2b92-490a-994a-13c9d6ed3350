import java.util.*;

/**
 * 权重计算服务 - 核心算法实现
 */
public class WeightCalculationService {

    /**
     * 计算最优权重分配
     * @param currentQps 当前QPS
     * @param config 动态策略配置
     * @return 权重计算结果
     */
    public WeightCalculationResult calculateOptimalWeights(double currentQps, DynamicStrategyConfig config) {
        long startTime = System.currentTimeMillis();
        List<AdjustmentStep> steps = new ArrayList<>();
        
        try {
            System.out.println("\n=== 开始权重计算 ===");
            System.out.println("当前QPS: " + currentQps);
            System.out.println("最大QPS: " + config.getMaxQPS());
            System.out.println("安全缓冲: " + config.getSafetyBuffer());
            
            // 1. 计算安全容量
            double safeMaxQps = config.getMaxQPS() * config.getSafetyBuffer();
            System.out.println("安全容量: " + safeMaxQps);
            
            // 2. 验证目标权重可行性
            double targetMultiplier = calculateMultiplier(config.getTargetWeights(), config);
            double targetQps = currentQps * targetMultiplier;
            
            System.out.println("目标权重: " + config.getTargetWeights());
            System.out.println("目标倍数: " + String.format("%.2f", targetMultiplier));
            System.out.println("目标QPS: " + String.format("%.0f", targetQps));
            
            if (targetQps <= safeMaxQps) {
                // 直接使用目标权重
                System.out.println("✅ 目标QPS在安全范围内，直接使用目标权重");
                return buildSuccessResult(config.getTargetWeights(), targetQps, safeMaxQps, 
                    CalculationStatus.TARGET_ACHIEVED, "目标权重可直接使用", steps, startTime);
            }
            
            System.out.println("❌ 目标QPS超出安全容量，启动容量优化算法");
            
            // 3. 启动容量优化算法
            Map<String, Double> optimizedWeights = optimizeWeights(currentQps, safeMaxQps, config, steps);
            
            if (optimizedWeights != null) {
                double optimizedQps = currentQps * calculateMultiplier(optimizedWeights, config);
                System.out.println("✅ 找到优化权重: " + optimizedWeights);
                return buildSuccessResult(optimizedWeights, optimizedQps, safeMaxQps, 
                    CalculationStatus.CAPACITY_OPTIMIZED, "通过权重优化满足容量约束", steps, startTime);
            }
            
            // 4. 使用紧急保护权重
            System.out.println("⚠️ 无法找到可行的优化方案，启用紧急保护权重");
            Map<String, Double> emergencyWeights = config.getEmergencyConfig().getWeights();
            double emergencyQps = currentQps * calculateMultiplier(emergencyWeights, config);
            
            return buildSuccessResult(emergencyWeights, emergencyQps, safeMaxQps, 
                CalculationStatus.EMERGENCY_PROTECTION, "启用紧急保护权重", steps, startTime);
                
        } catch (Exception e) {
            System.err.println("权重计算失败: " + e.getMessage());
            return buildFailureResult(e.getMessage(), startTime);
        }
    }
    
    /**
     * 容量优化算法
     */
    private Map<String, Double> optimizeWeights(double currentQps, double safeMaxQps, 
            DynamicStrategyConfig config, List<AdjustmentStep> steps) {
        
        double maxMultiplier = safeMaxQps / currentQps;
        System.out.println("最大允许倍数: " + String.format("%.2f", maxMultiplier));
        
        Map<String, Double> currentWeights = new HashMap<>(config.getTargetWeights());
        
        // 优先级1：减少B策略权重（因为B策略资源消耗更大）
        double[] scaleFactors = {0.8, 0.6, 0.4, 0.2, 0.1};
        
        System.out.println("\n--- 开始权重调整优化 ---");
        for (double scaleFactor : scaleFactors) {
            Map<String, Double> adjustedWeights = adjustWeights(currentWeights, scaleFactor);
            double multiplier = calculateMultiplier(adjustedWeights, config);
            double expectedQps = currentQps * multiplier;
            boolean feasible = multiplier <= maxMultiplier;
            
            AdjustmentStep step = new AdjustmentStep(
                "将B策略权重缩放至 " + String.format("%.1f", scaleFactor * 100) + "%",
                new HashMap<>(adjustedWeights),
                multiplier,
                expectedQps,
                feasible
            );
            steps.add(step);
            
            System.out.println("尝试调整: " + step.getStepDescription());
            System.out.println("  调整后权重: " + adjustedWeights);
            System.out.println("  预期倍数: " + String.format("%.2f", multiplier));
            System.out.println("  预期QPS: " + String.format("%.0f", expectedQps));
            System.out.println("  是否可行: " + (feasible ? "✅" : "❌"));
            
            if (feasible) {
                System.out.println("找到可行解!");
                return adjustedWeights;
            }
        }
        
        return null; // 无可行解
    }
    
    /**
     * 调整权重：减少B策略权重，增加A策略权重
     */
    private Map<String, Double> adjustWeights(Map<String, Double> originalWeights, double bScaleFactor) {
        Map<String, Double> adjustedWeights = new HashMap<>();
        
        double originalBWeight = originalWeights.get("B");
        double newBWeight = originalBWeight * bScaleFactor;
        double weightDiff = originalBWeight - newBWeight;
        
        // B策略权重减少
        adjustedWeights.put("B", newBWeight);
        
        // A策略权重增加，保持总权重为1.0
        double originalAWeight = originalWeights.get("A");
        double newAWeight = originalAWeight + weightDiff;
        adjustedWeights.put("A", newAWeight);
        
        return adjustedWeights;
    }
    
    /**
     * 计算权重倍数：A权重×1 + B权重×3
     */
    private double calculateMultiplier(Map<String, Double> weights, DynamicStrategyConfig config) {
        double multiplier = 0.0;
        for (Map.Entry<String, Double> entry : weights.entrySet()) {
            String strategyName = entry.getKey();
            double weight = entry.getValue();
            int batchNum = config.getStrategies().get(strategyName).getBatchNum();
            multiplier += weight * batchNum;
        }
        return multiplier;
    }
    
    /**
     * 构建成功结果
     */
    private WeightCalculationResult buildSuccessResult(Map<String, Double> finalWeights, 
            double expectedQps, double safeMaxQps, CalculationStatus status, String reason,
            List<AdjustmentStep> steps, long startTime) {
        
        long calculationTime = System.currentTimeMillis() - startTime;
        double resourceUtilization = expectedQps / safeMaxQps;
        
        return new WeightCalculationResult(status, finalWeights, expectedQps, 
            resourceUtilization, reason, calculationTime, steps);
    }
    
    /**
     * 构建失败结果
     */
    private WeightCalculationResult buildFailureResult(String errorMessage, long startTime) {
        long calculationTime = System.currentTimeMillis() - startTime;
        return new WeightCalculationResult(CalculationStatus.CALCULATION_FAILED, 
            new HashMap<>(), 0, 0, errorMessage, calculationTime, new ArrayList<>());
    }
}
